import { describe, test, expect } from '@jest/globals';
import { 
  prisma, 
  setupTestDatabase, 
  createTestUser, 
  createTestWebhook, 
  createTestDomain,
  createTest<PERSON>lia<PERSON>,
  createTestDomainWithCatchAll} from '../../setup/test-db-setup';

setupTestDatabase();

// Simulate the webhook lookup logic from email processing (new architecture)
function simulateWebhookLookup(domainConfig, emailAddress) {
  // 0. Check if domain is active
  if (!domainConfig || !domainConfig.active) {
    return null;
  }

  // 1. Check for specific alias configuration first
  const specificAlias = domainConfig.aliases.find((alias) =>
    alias.email === emailAddress && alias.active && alias.webhook
  );

  if (specificAlias && specificAlias.webhook) {
    return specificAlias.webhook.url;
  }

  // 2. Fall back to catch-all alias webhook (new architecture)
  const catchAllAlias = domainConfig.aliases.find((alias) =>
    alias.email.startsWith('*@') && alias.active && alias.webhook
  );

  if (catchAllAlias && catchAllAlias.webhook) {
    return catchAllAlias.webhook.url;
  }

  // 3. No webhook configured
  return null;
}

describe('Webhook Processing Logic', () => {
  test('should route emails to correct webhooks based on aliases and domain defaults', async () => {
    // Step 1: Create test user
    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Webhook Test User',
    });

    // Step 2: Create webhooks
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default Webhook',
      url: 'https://api.example.com/domain-webhook',
      description: 'Default webhook for domain emails',
    });

    const supportWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Team Webhook',
      url: 'https://support.example.com/webhook',
      description: 'Webhook for support emails',
    });

    const salesWebhook = await createTestWebhook(testUser.id, {
      name: 'Sales Team Webhook',
      url: 'https://sales.example.com/webhook',
      description: 'Webhook for sales emails',
    });

    // Step 3: Create domain with default webhook
    const testDomain = await createTestDomainWithCatchAll(testUser.id, domainWebhook.id, {
      domain: 'webhook-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Step 4: Create aliases with specific webhooks
    const supportAlias = await createTestAlias(testDomain.domain.id, supportWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    const salesAlias = await createTestAlias(testDomain.domain.id, salesWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Step 5: Get domain config for testing (simulating email processing)
    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'webhook-test.com' },
      include: { aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    expect(domainConfig).toBeTruthy();
    expect(domainConfig.aliases).toHaveLength(3); // 2 specific + 1 catch-all

    // Test 1: Support email should use support webhook
    const supportWebhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(supportWebhookUrl).toBe(supportWebhook.url);

    // Test 2: Sales email should use sales webhook  
    const salesWebhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(salesWebhookUrl).toBe(salesWebhook.url);

    // Test 3: Random email should use domain default webhook
    const randomWebhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(randomWebhookUrl).toBe(domainWebhook.url);
  });

  test('should fallback to domain default when alias is inactive', async () => {
    const testUser = await createTestUser();
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default',
      url: 'https://api.example.com/default',
    });
    const supportWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Webhook',
      url: 'https://support.example.com/webhook',
    });

    const testDomain = await createTestDomainWithCatchAll(testUser.id, domainWebhook.id, {
      domain: 'fallback-test.com',
    });

    // Create support alias and then deactivate it
    const supportAlias = await createTestAlias(testDomain.domain.id, supportWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Now deactivate the alias to test fallback
    await prisma.alias.update({
      where: { id: supportAlias.id },
      data: { active: false }
    });

    // Get domain config with only active aliases
    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'fallback-test.com' },
      include: { aliases: {
          include: { webhook: true },
          where: { active: true } // Only active aliases
        }
      },
    });

    // Support email should fallback to catch-all alias (support alias is inactive)
    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBe(domainWebhook.url); // Should use catch-all alias webhook
    expect(domainConfig.aliases).toHaveLength(1); // Only catch-all alias active
  });

  test('should return null for inactive domains', async () => {
    const testUser = await createTestUser();
    const domainWebhook = await createTestWebhook(testUser.id);
    
    const testDomain = await createTestDomainWithCatchAll(testUser.id, domainWebhook.id, {
      domain: 'inactive-test.com',
      active: false, // Inactive domain
    });

    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'inactive-test.com' },
      include: { aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    // Should return null for inactive domain
    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBeNull();
  });

  test('should handle domain without webhook gracefully', async () => {
    const testUser = await createTestUser();
    const webhook = await createTestWebhook(testUser.id);
    
    // Create domain normally first
    const testDomain = await createTestDomainWithCatchAll(testUser.id, webhook.id, {
      domain: 'no-webhook-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });
    
    // Simulate a domain query that might have null webhook
    const domainConfig = {
      ...testDomain,
      webhook: null, // Simulate missing webhook
      aliases: [],
      active: true,
    };

    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBeNull();
  });

  test('should prioritize alias webhook over domain default', async () => {
    const testUser = await createTestUser();
    
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default',
      url: 'https://domain.example.com/webhook',
    });
    
    const aliasWebhook = await createTestWebhook(testUser.id, {
      name: 'Specific Alias Webhook',
      url: 'https://specific.example.com/webhook',
    });

    const testDomain = await createTestDomainWithCatchAll(testUser.id, domainWebhook.id);
    
    await createTestAlias(testDomain.domain.id, aliasWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    const domainConfig = await prisma.domain.findUnique({
      where: { id: testDomain.domain.id },
      include: { aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    // Should use alias webhook, not domain default
    const webhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(webhookUrl).toBe(aliasWebhook.url);
    expect(webhookUrl).not.toBe(domainWebhook.url);
  });

  test('should handle multiple aliases for same domain correctly', async () => {
    const testUser = await createTestUser();
    const domainWebhook = await createTestWebhook(testUser.id, { url: 'https://domain.example.com/webhook' });
    const supportWebhook = await createTestWebhook(testUser.id, { url: 'https://support.example.com/webhook' });
    const billingWebhook = await createTestWebhook(testUser.id, { url: 'https://billing.example.com/webhook' });

    const testDomain = await createTestDomainWithCatchAll(testUser.id, domainWebhook.id, {
      domain: 'multi-alias.com',
    });

    // Create multiple aliases
    await createTestAlias(testDomain.domain.id, supportWebhook.id, { email: '<EMAIL>' });
    await createTestAlias(testDomain.domain.id, billingWebhook.id, { email: '<EMAIL>' });

    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'multi-alias.com' },
      include: { aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    // Each alias should route to its specific webhook
    expect(simulateWebhookLookup(domainConfig, '<EMAIL>')).toBe(supportWebhook.url);
    expect(simulateWebhookLookup(domainConfig, '<EMAIL>')).toBe(billingWebhook.url);
    expect(simulateWebhookLookup(domainConfig, '<EMAIL>')).toBe(domainWebhook.url);
  });

  test('should properly simulate complete email processing workflow', async () => {
    // This test replicates the exact patterns from the legacy script
    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Complete Test User',
      verified: true,
    });

    // Create three webhooks like in legacy test
    const domainWebhook = await createTestWebhook(testUser.id, {
      name: 'Domain Default Webhook',
      url: 'https://api.example.com/domain-webhook',
      description: 'Default webhook for domain emails',
    });

    const supportWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Team Webhook',
      url: 'https://support.example.com/webhook',
      description: 'Webhook for support emails',
    });

    const salesWebhook = await createTestWebhook(testUser.id, {
      name: 'Sales Team Webhook',
      url: 'https://sales.example.com/webhook',
      description: 'Webhook for sales emails',
    });

    // Create domain exactly like legacy test
    const testDomain = await createTestDomainWithCatchAll(testUser.id, domainWebhook.id, {
      domain: 'complete-webhook-test.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Create aliases exactly like legacy test
    const supportAlias = await createTestAlias(testDomain.domain.id, supportWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    const salesAlias = await createTestAlias(testDomain.domain.id, salesWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Test webhook lookup logic exactly like legacy test
    const domainConfigForTest = await prisma.domain.findUnique({
      where: { domain: 'complete-webhook-test.com' },
      include: { aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    // Test 1: Support email routing
    const supportResult = simulateWebhookLookup(domainConfigForTest, '<EMAIL>');
    expect(supportResult).toBe(supportWebhook.url);

    // Test 2: Sales email routing
    const salesResult = simulateWebhookLookup(domainConfigForTest, '<EMAIL>');
    expect(salesResult).toBe(salesWebhook.url);

    // Test 3: Random email fallback
    const randomResult = simulateWebhookLookup(domainConfigForTest, '<EMAIL>');
    expect(randomResult).toBe(domainWebhook.url);

    // Test 4: Inactive alias fallback (like in legacy test Step 6)
    await prisma.alias.update({
      where: { id: supportAlias.id },
      data: { active: false }
    });

    const domainConfigWithInactiveSupport = await prisma.domain.findUnique({
      where: { domain: 'complete-webhook-test.com' },
      include: { aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    const inactiveResult = simulateWebhookLookup(domainConfigWithInactiveSupport, '<EMAIL>');
    expect(inactiveResult).toBe(domainWebhook.url); // Should fallback to catch-all alias

    // Verify test completeness
    expect(domainConfigWithInactiveSupport.aliases).toHaveLength(2); // Sales alias + catch-all alias active
    // expect(domainConfigWithInactiveSupport.webhook.url).toBe(domainWebhook.url); // TODO: Update for new architecture
  });

  test('should reproduce X-Original-To header bug - alias emails routed to catch-all webhook', async () => {
    // This test reproduces the production bug where emails to aliases are routed to catch-all webhook
    // because the webhook lookup uses To: header instead of X-Original-To header

    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Bug Test User',
      verified: true,
    });

    // Create webhooks
    const catchAllWebhook = await createTestWebhook(testUser.id, {
      name: 'Catch-All Webhook',
      url: 'https://site1.com/webhook',
      description: 'Default catch-all webhook',
      verified: true,
    });

    const aliasWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Alias Webhook',
      url: 'https://site2.com/webhook',
      description: 'Specific webhook for support alias',
      verified: true,
    });

    // Create domain with catch-all
    const testDomain = await createTestDomainWithCatchAll(testUser.id, catchAllWebhook.id, {
      domain: 'bugtest.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Create specific alias with its own webhook
    const supportAlias = await createTestAlias(testDomain.domain.id, aliasWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Simulate email with different To: and X-Original-To headers
    // This simulates what happens in production where:
    // - To: header might contain the original recipient
    // - X-Original-To: header contains the actual alias that was targeted
    const rawEmailWithHeaders = `From: <EMAIL>
To: <EMAIL>
X-Original-To: <EMAIL>
Subject: Bug Reproduction Test
Message-ID: <<EMAIL>>
Date: ${new Date().toUTCString()}

This email should go to the support alias webhook, not the catch-all webhook.`;

    // Import EmailParser to test the actual parsing logic
    const { EmailParser } = await import('../../../src/backend/services/email-parser.js');

    // Parse the email like the actual system does
    const parsedEmail = await EmailParser.parseToWebhookPayload(rawEmailWithHeaders, 'bugtest.com');

    // Get domain config like the actual system does
    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'bugtest.com' },
      include: {
        aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    // Test the current (buggy) behavior - using To: header for webhook lookup
    const buggyWebhookUrl = simulateWebhookLookup(domainConfig, parsedEmail.message.recipient.email);

    // Test the correct behavior - using X-Original-To header for webhook lookup
    const correctWebhookUrl = simulateWebhookLookup(domainConfig, parsedEmail.envelope.processed.alias);

    // Verify the bug exists

    // The bug: using To: header routes to catch-all instead of alias webhook
    expect(parsedEmail.message.recipient.email).toBe('<EMAIL>'); // From To: header
    expect(parsedEmail.envelope.processed.alias).toBe('<EMAIL>'); // From X-Original-To header
    expect(buggyWebhookUrl).toBe(catchAllWebhook.url); // BUG: Routes to catch-all
    expect(correctWebhookUrl).toBe(aliasWebhook.url); // CORRECT: Should route to alias webhook

    // This demonstrates the bug - the same email gets routed differently depending on which header is used
    expect(buggyWebhookUrl).not.toBe(correctWebhookUrl);
  });

  test('should correctly route emails using X-Original-To header after fix', async () => {
    // This test verifies that the fix works - emails should be routed based on X-Original-To header

    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Fix Test User',
      verified: true,
    });

    // Create webhooks
    const catchAllWebhook = await createTestWebhook(testUser.id, {
      name: 'Catch-All Webhook',
      url: 'https://site1.com/webhook',
      description: 'Default catch-all webhook',
      verified: true,
    });

    const aliasWebhook = await createTestWebhook(testUser.id, {
      name: 'Support Alias Webhook',
      url: 'https://site2.com/webhook',
      description: 'Specific webhook for support alias',
      verified: true,
    });

    // Create domain with catch-all
    const testDomain = await createTestDomainWithCatchAll(testUser.id, catchAllWebhook.id, {
      domain: 'fixtest.com',
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Create specific alias with its own webhook
    const supportAlias = await createTestAlias(testDomain.domain.id, aliasWebhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Simulate email with different To: and X-Original-To headers
    const rawEmailWithHeaders = `From: <EMAIL>
To: <EMAIL>
X-Original-To: <EMAIL>
Subject: Fix Verification Test
Message-ID: <<EMAIL>>
Date: ${new Date().toUTCString()}

This email should go to the support alias webhook after the fix.`;

    // Import EmailParser to test the actual parsing logic
    const { EmailParser } = await import('../../../src/backend/services/email-parser.js');

    // Parse the email like the actual system does
    const parsedEmail = await EmailParser.parseToWebhookPayload(rawEmailWithHeaders, 'fixtest.com');

    // Get domain config like the actual system does
    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'fixtest.com' },
      include: {
        aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    // Test the fixed behavior - using X-Original-To header for webhook lookup (this is what the fix does)
    const fixedWebhookUrl = simulateWebhookLookup(domainConfig, parsedEmail.envelope.processed.alias);

    // Verify the fix works correctly
    expect(parsedEmail.message.recipient.email).toBe('<EMAIL>'); // From To: header
    expect(parsedEmail.envelope.processed.alias).toBe('<EMAIL>'); // From X-Original-To header
    expect(fixedWebhookUrl).toBe(aliasWebhook.url); // FIXED: Now correctly routes to alias webhook

    // Additional verification: ensure catch-all still works for non-alias emails
    const nonAliasWebhookUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(nonAliasWebhookUrl).toBe(catchAllWebhook.url); // Should still use catch-all for non-alias emails
  });

  test('should handle exact production scenario described by user', async () => {
    // This test replicates the exact production scenario:
    // - Domain has catch-all pointing to https://site1.com
    // - Created alias support@samedomain with webhook (verified=true) https://site2.com
    // - Email sent to support alias should go to site2.com, not site1.com

    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Production User',
      verified: true,
    });

    // Create webhooks exactly as described
    const site1Webhook = await createTestWebhook(testUser.id, {
      name: 'Site1 Catch-All Webhook',
      url: 'https://site1.com',
      description: 'Domain catch-all webhook',
      verified: true,
    });

    const site2Webhook = await createTestWebhook(testUser.id, {
      name: 'Site2 Support Webhook',
      url: 'https://site2.com',
      description: 'Support alias webhook',
      verified: true,
    });

    // Create domain with catch-all pointing to site1.com
    const testDomain = await createTestDomainWithCatchAll(testUser.id, site1Webhook.id, {
      domain: 'samedomain.com', // Using "samedomain" as mentioned by user
      verified: true,
      verificationStatus: 'VERIFIED',
      active: true,
    });

    // Create support alias with verified webhook pointing to site2.com
    const supportAlias = await createTestAlias(testDomain.domain.id, site2Webhook.id, {
      email: '<EMAIL>',
      active: true,
    });

    // Verify webhook is marked as verified (as mentioned by user)
    const verifiedWebhook = await prisma.webhook.findUnique({
      where: { id: site2Webhook.id }
    });
    expect(verifiedWebhook.verified).toBe(true);

    // Simulate production <NAME_EMAIL>
    const productionEmail = `From: <EMAIL>
To: <EMAIL>
X-Original-To: <EMAIL>
Subject: Production Support Request
Message-ID: <<EMAIL>>
Date: ${new Date().toUTCString()}

This is a production support request that should go to site2.com webhook.`;

    // Parse email like the system does
    const { EmailParser } = await import('../../../src/backend/services/email-parser.js');
    const parsedEmail = await EmailParser.parseToWebhookPayload(productionEmail, 'samedomain.com');

    // Get domain config
    const domainConfig = await prisma.domain.findUnique({
      where: { domain: 'samedomain.com' },
      include: {
        aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
    });

    // Test the fixed behavior - should route to support alias webhook, not catch-all
    const webhookUrl = simulateWebhookLookup(domainConfig, parsedEmail.envelope.processed.alias);

    // Verify the production scenario works correctly after the fix
    expect(parsedEmail.envelope.processed.alias).toBe('<EMAIL>');
    expect(webhookUrl).toBe('https://site2.com'); // Should go to support webhook
    expect(webhookUrl).not.toBe('https://site1.com'); // Should NOT go to catch-all

    // Additional verification: test that other emails still go to catch-all
    const randomEmailUrl = simulateWebhookLookup(domainConfig, '<EMAIL>');
    expect(randomEmailUrl).toBe('https://site1.com'); // Non-alias emails should still use catch-all

    console.log('✅ Production scenario test passed:');
    console.log(`   <NAME_EMAIL> → ${webhookUrl} (correct)`);
    console.log(`   <NAME_EMAIL> → ${randomEmailUrl} (catch-all)`);
  });
});
