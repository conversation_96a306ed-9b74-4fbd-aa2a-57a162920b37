<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useModal } from '@composables/useModal'
import { useToast } from '@composables/useToast'
import { useConfirm } from '@composables/useConfirm'
import { useNotificationToasts } from '@composables/useNotificationToasts'
import router from './router'
import ModalSystem from './components/ui/ModalSystem.vue'
import ToastContainer from './components/ui/ToastContainer.vue'
import ConfirmDialog from './components/ui/ConfirmDialog.vue'
import DomainForm from './components/forms/DomainForm.vue'
import WebhookForm from './components/forms/WebhookForm.vue'
import AliasForm from './components/forms/AliasForm.vue'
import DomainVerification from './components/modals/DomainVerification.vue'
import WebhookVerification from './components/modals/WebhookVerification.vue'
import WebhookTestModal from './components/modals/WebhookTestModal.vue'

import UserLayout from './layouts/UserLayout.vue'
import GuestLayout from './layouts/GuestLayout.vue'
import AuthLayout from './layouts/AuthLayout.vue'

const route = useRoute()

// Determine layout based on route meta
const currentLayout = computed(() => {
  return route.meta?.layout || 'user'
})

const isReady = ref(false)
const { openModal, closeModal } = useModal()
const { confirmState, confirm, cancel, close } = useConfirm()

// Initialize toast system
useToast()

// Initialize notification toast integration
useNotificationToasts()

onMounted(() => {
  isReady.value = true

  // Make Vue modal system available globally (replace Alpine.js bridge)
  try {
    ;(window as any).openModal = openModal
    ;(window as any).closeModal = closeModal
  } catch (error) {
    console.warn('Could not set global modal functions, using vueModal instead:', error)
  }

  // Always provide vueModal for explicit Vue usage
  ;(window as any).vueModal = {
    open: openModal,
    close: closeModal
  }
})


</script>

<template>
  <div v-if="isReady">
    <!-- Toast System -->
    <ToastContainer />

    <!-- Confirm Dialog System -->
    <ConfirmDialog
      :is-open="confirmState.isOpen"
      :title="confirmState.title"
      :message="confirmState.message"
      :details="confirmState.details"
      :confirm-text="confirmState.confirmText"
      :cancel-text="confirmState.cancelText"
      :type="confirmState.type"
      :loading="confirmState.loading"
      @confirm="confirm"
      @cancel="cancel"
      @close="close"
    />

    <!-- Modal System -->
    <ModalSystem>
      <!-- Register modal components -->
      <template #create-domain="{ data, close }">
        <DomainForm
          :initial-data="data"
          @success="(domain) => {
            if (data.onSuccess) data.onSuccess(domain)
            close()
          }"
          @cancel="() => {
            if (data.onCancel) data.onCancel()
            close()
          }"
        />
      </template>

      <template #create-webhook="{ data, close }">
        <WebhookForm :initial-data="data" @success="close" @cancel="close" />
      </template>

      <template #create-alias="{ data, close }">
        <AliasForm :initial-data="data" @success="close" @cancel="close" />
      </template>

      <template #domain-verification="{ data, close }">
        <DomainVerification :domain-data="data" @close="close" />
      </template>

      <template #edit-domain="{ data, close }">
        <DomainForm
          :initial-data="data"
          :is-edit-mode="true"
          @success="(domain) => {
            if (data.onSuccess) data.onSuccess(domain)
            close()
          }"
          @cancel="() => {
            if (data.onCancel) data.onCancel()
            close()
          }"
        />
      </template>

      <template #edit-webhook="{ data, close }">
        <WebhookForm
          :initial-data="data"
          :is-edit-mode="true"
          @success="close"
          @cancel="close"
        />
      </template>

      <template #edit-alias="{ data, close }">
        <AliasForm
          :initial-data="data"
          :is-edit-mode="true"
          @success="close"
          @cancel="close"
        />
      </template>

      <template #webhook-verification="{ data, close }">
        <WebhookVerification :webhook-data="data" @close="close" />
      </template>

      <template #webhook-test="{ data, close }">
        <WebhookTestModal :webhook-data="data" @close="close" />
      </template>


    </ModalSystem>

    <!-- Dynamic layout based on route -->
    <UserLayout v-if="currentLayout === 'user'">
      <router-view />
    </UserLayout>
    
    <GuestLayout v-else-if="currentLayout === 'guest'">
      <router-view />
    </GuestLayout>
    
    <AuthLayout v-else-if="currentLayout === 'auth'">
      <router-view />
    </AuthLayout>
    
    <!-- Fallback -->
    <router-view v-else />
  </div>
</template>

<style scoped>
/* Dashboard-specific styles */
</style>