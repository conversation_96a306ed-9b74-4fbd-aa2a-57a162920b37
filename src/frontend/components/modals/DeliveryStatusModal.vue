<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  statusData: {
    logId: string
    deliveryStatus: 'DELIVERED' | 'FAILED' | 'RETRYING' | 'PENDING' | 'EXPIRED'
    deliveryAttempts: number
    errorMessage?: string | null
    deliveredAt?: string | null
    lastAttemptAt?: string | null
    messageId: string
    fromAddress: string
    toAddresses: string[]
    subject?: string | null
    alias?: string
    domain?: string
    webhookUrl?: string
    timestamp: string
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// State
const copySuccess = ref(false)

// Computed
const statusConfig = computed(() => {
  const configs = {
    'DELIVERED': {
      icon: '✅',
      color: 'text-success',
      bgColor: 'bg-success/10',
      title: 'Successfully Delivered',
      description: 'Email was successfully delivered to the webhook endpoint.'
    },
    'FAILED': {
      icon: '❌',
      color: 'text-error',
      bgColor: 'bg-error/10',
      title: 'Delivery Failed',
      description: 'Email delivery failed after all retry attempts.'
    },
    'RETRYING': {
      icon: '🔄',
      color: 'text-warning',
      bgColor: 'bg-warning/10',
      title: 'Retrying Delivery',
      description: 'Email delivery failed and is being retried.'
    },
    'PENDING': {
      icon: '⏳',
      color: 'text-info',
      bgColor: 'bg-info/10',
      title: 'Pending Delivery',
      description: 'Email is queued for delivery.'
    },
    'EXPIRED': {
      icon: '⏰',
      color: 'text-neutral',
      bgColor: 'bg-neutral/10',
      title: 'Delivery Expired',
      description: 'Email delivery attempts have expired.'
    }
  }
  return configs[props.statusData.deliveryStatus] || configs['PENDING']
})

const formattedErrorMessage = computed(() => {
  if (!props.statusData.errorMessage) return null
  
  try {
    // Try to parse as JSON for better formatting
    const parsed = JSON.parse(props.statusData.errorMessage)
    return JSON.stringify(parsed, null, 2)
  } catch {
    // If not JSON, return as-is
    return props.statusData.errorMessage
  }
})

const deliveryDetails = computed(() => {
  const details = []
  
  if (props.statusData.deliveryAttempts > 0) {
    details.push({
      label: 'Delivery Attempts',
      value: `${props.statusData.deliveryAttempts} attempt${props.statusData.deliveryAttempts !== 1 ? 's' : ''}`
    })
  }
  
  if (props.statusData.deliveredAt) {
    details.push({
      label: 'Delivered At',
      value: new Date(props.statusData.deliveredAt).toLocaleString()
    })
  }
  
  if (props.statusData.lastAttemptAt) {
    details.push({
      label: 'Last Attempt',
      value: new Date(props.statusData.lastAttemptAt).toLocaleString()
    })
  }
  
  if (props.statusData.webhookUrl) {
    details.push({
      label: 'Webhook URL',
      value: props.statusData.webhookUrl
    })
  }
  
  return details
})

// Methods
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
  }
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    'DELIVERED': 'badge-success',
    'FAILED': 'badge-error',
    'RETRYING': 'badge-warning',
    'PENDING': 'badge-info',
    'EXPIRED': 'badge-neutral'
  }
  return classes[status] || 'badge-neutral'
}
</script>

<template>
  <div class="space-y-6">
    <!-- Status Header -->
    <div :class="['rounded-lg p-4', statusConfig.bgColor]">
      <div class="flex items-center gap-3">
        <span class="text-2xl">{{ statusConfig.icon }}</span>
        <div>
          <h3 :class="['text-lg font-semibold', statusConfig.color]">
            {{ statusConfig.title }}
          </h3>
          <p class="text-sm text-base-content/70">
            {{ statusConfig.description }}
          </p>
        </div>
      </div>
    </div>

    <!-- Email Details -->
    <div class="bg-base-200 rounded-lg p-4">
      <h4 class="font-medium text-base-content mb-3">Email Details</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
        <div>
          <span class="font-medium text-base-content/70">Message ID:</span>
          <span class="ml-2 font-mono text-xs break-all">{{ statusData.messageId }}</span>
        </div>
        <div>
          <span class="font-medium text-base-content/70">Status:</span>
          <span :class="['badge badge-sm ml-2', getStatusBadgeClass(statusData.deliveryStatus)]">
            {{ statusData.deliveryStatus }}
          </span>
        </div>
        <div>
          <span class="font-medium text-base-content/70">From:</span>
          <span class="ml-2 break-all">{{ statusData.fromAddress }}</span>
        </div>
        <div>
          <span class="font-medium text-base-content/70">To:</span>
          <span class="ml-2 break-all">{{ statusData.toAddresses.join(', ') }}</span>
        </div>
        <div v-if="statusData.subject">
          <span class="font-medium text-base-content/70">Subject:</span>
          <span class="ml-2">{{ statusData.subject }}</span>
        </div>
        <div v-if="statusData.alias">
          <span class="font-medium text-base-content/70">Alias:</span>
          <span class="ml-2">{{ statusData.alias }}</span>
        </div>
        <div v-if="statusData.domain">
          <span class="font-medium text-base-content/70">Domain:</span>
          <span class="ml-2">{{ statusData.domain }}</span>
        </div>
        <div>
          <span class="font-medium text-base-content/70">Received:</span>
          <span class="ml-2">{{ new Date(statusData.timestamp).toLocaleString() }}</span>
        </div>
      </div>
    </div>

    <!-- Delivery Details -->
    <div v-if="deliveryDetails.length > 0" class="bg-base-200 rounded-lg p-4">
      <h4 class="font-medium text-base-content mb-3">Delivery Information</h4>
      <div class="space-y-2 text-sm">
        <div v-for="detail in deliveryDetails" :key="detail.label" class="flex flex-col sm:flex-row sm:items-center gap-1">
          <span class="font-medium text-base-content/70 min-w-[120px]">{{ detail.label }}:</span>
          <span class="break-all">{{ detail.value }}</span>
        </div>
      </div>
    </div>

    <!-- Error Details -->
    <div v-if="formattedErrorMessage" class="bg-base-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-3">
        <h4 class="font-medium text-base-content">Error Details</h4>
        <button 
          type="button"
          @click="copyToClipboard(formattedErrorMessage)"
          class="btn btn-outline btn-xs"
          :class="{ 'btn-success': copySuccess }"
        >
          <svg v-if="!copySuccess" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <svg v-else class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          {{ copySuccess ? 'Copied!' : 'Copy' }}
        </button>
      </div>
      <div class="bg-base-100 border border-base-300 rounded-lg overflow-hidden">
        <div class="max-h-48 overflow-y-auto">
          <pre class="p-3 text-sm font-mono leading-relaxed whitespace-pre-wrap break-words text-error">{{ formattedErrorMessage }}</pre>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-2 pt-4">
      <button 
        type="button"
        @click="emit('close')"
        class="btn btn-primary"
      >
        Close
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for error message */
.max-h-48::-webkit-scrollbar {
  width: 8px;
}

.max-h-48::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

.max-h-48::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.2);
  border-radius: 4px;
}

.max-h-48::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.3);
}
</style>
