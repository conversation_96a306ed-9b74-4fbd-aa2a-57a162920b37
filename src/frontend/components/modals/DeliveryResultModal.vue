<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  resultData: {
    logId: string
    deliveryStatus: 'DELIVERED' | 'FAILED' | 'RETRYING' | 'PENDING' | 'EXPIRED'
    deliveryAttempts: number
    errorMessage?: string | null
    deliveredAt?: string | null
    lastAttemptAt?: string | null
    messageId: string
    fromAddress: string
    toAddresses: string[]
    subject?: string | null
    alias?: string
    domain?: string
    webhookUrl?: string
    webhookPayload?: any
    timestamp: string
    httpStatus?: number | null
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// State
const copySuccess = ref(false)

// Computed
const statusConfig = computed(() => {
  const status = props.resultData.deliveryStatus
  const httpStatus = props.resultData.httpStatus
  
  if (status === 'DELIVERED') {
    return {
      icon: '✅',
      color: 'text-success',
      bgColor: 'bg-success/10',
      title: `${httpStatus || 200} OK - Delivered`,
      description: 'Webhook was successfully delivered'
    }
  } else if (status === 'FAILED') {
    const statusCode = httpStatus || 'Unknown'
    const statusText = getHttpStatusText(httpStatus)
    return {
      icon: '❌',
      color: 'text-error',
      bgColor: 'bg-error/10',
      title: `${statusCode} ${statusText} - Failed`,
      description: 'Webhook delivery failed'
    }
  } else if (status === 'RETRYING') {
    return {
      icon: '🔄',
      color: 'text-warning',
      bgColor: 'bg-warning/10',
      title: 'Retrying delivery',
      description: `Attempt ${props.resultData.deliveryAttempts} of 3`
    }
  } else if (status === 'PENDING') {
    return {
      icon: '⏳',
      color: 'text-info',
      bgColor: 'bg-info/10',
      title: 'Pending delivery',
      description: 'Webhook is queued for delivery'
    }
  } else {
    return {
      icon: '⏰',
      color: 'text-neutral',
      bgColor: 'bg-neutral/10',
      title: 'Delivery expired',
      description: 'Webhook delivery attempts have expired'
    }
  }
})

const contentToShow = computed(() => {
  if (props.resultData.deliveryStatus === 'DELIVERED' && props.resultData.webhookPayload) {
    // Show the payload for successful deliveries
    try {
      return {
        type: 'payload',
        title: 'Webhook payload',
        content: JSON.stringify(props.resultData.webhookPayload, null, 2)
      }
    } catch {
      return {
        type: 'payload',
        title: 'Webhook payload',
        content: 'Invalid JSON payload'
      }
    }
  } else if (props.resultData.errorMessage) {
    // Show error message for failed deliveries
    return {
      type: 'error',
      title: 'Error details',
      content: formatErrorMessage(props.resultData.errorMessage)
    }
  } else {
    // Fallback for other statuses
    return {
      type: 'info',
      title: 'Status information',
      content: `Status: ${props.resultData.deliveryStatus}\nAttempts: ${props.resultData.deliveryAttempts}`
    }
  }
})

const metadataItems = computed(() => {
  const items = [
    { label: 'Message ID', value: props.resultData.messageId, mono: true },
    { label: 'From', value: props.resultData.fromAddress },
    { label: 'To', value: props.resultData.toAddresses.join(', ') },
  ]
  
  if (props.resultData.subject) {
    items.push({ label: 'Subject', value: props.resultData.subject })
  }
  
  if (props.resultData.alias) {
    items.push({ label: 'Alias', value: props.resultData.alias })
  }
  
  if (props.resultData.domain) {
    items.push({ label: 'Domain', value: props.resultData.domain })
  }
  
  if (props.resultData.webhookUrl) {
    items.push({ label: 'Webhook URL', value: props.resultData.webhookUrl, mono: true })
  }
  
  items.push({ label: 'Received', value: new Date(props.resultData.timestamp).toLocaleString() })
  
  if (props.resultData.deliveredAt) {
    items.push({ label: 'Delivered at', value: new Date(props.resultData.deliveredAt).toLocaleString() })
  }
  
  if (props.resultData.lastAttemptAt) {
    items.push({ label: 'Last attempt', value: new Date(props.resultData.lastAttemptAt).toLocaleString() })
  }
  
  return items
})

// Methods
const getHttpStatusText = (status?: number | null): string => {
  if (!status) return 'Error'
  
  const statusTexts: Record<number, string> = {
    200: 'OK',
    201: 'Created',
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    405: 'Method Not Allowed',
    408: 'Request Timeout',
    429: 'Too Many Requests',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout'
  }
  
  return statusTexts[status] || 'Error'
}

const formatErrorMessage = (error: string): string => {
  try {
    // Try to parse as JSON for better formatting
    const parsed = JSON.parse(error)
    return JSON.stringify(parsed, null, 2)
  } catch {
    // If not JSON, return as-is
    return error
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
  }
}

const getContentSize = (content: string): string => {
  const bytes = new Blob([content]).size
  if (bytes < 1024) return `${bytes} bytes`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}
</script>

<template>
  <div class="space-y-6">
    <!-- Status Header -->
    <div :class="['rounded-lg p-4', statusConfig.bgColor]">
      <div class="flex items-center gap-3">
        <span class="text-2xl">{{ statusConfig.icon }}</span>
        <div>
          <h3 :class="['text-lg font-semibold', statusConfig.color]">
            {{ statusConfig.title }}
          </h3>
          <p class="text-sm text-base-content/70">
            {{ statusConfig.description }}
          </p>
        </div>
      </div>
    </div>

    <!-- Email Metadata -->
    <div class="bg-base-200 rounded-lg p-4">
      <h4 class="font-medium text-base-content mb-3">Email details</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
        <div v-for="item in metadataItems" :key="item.label" class="flex flex-col sm:flex-row sm:items-start gap-1">
          <span class="font-medium text-base-content/70 min-w-[100px] shrink-0">{{ item.label }}:</span>
          <span :class="['break-all', item.mono ? 'font-mono text-xs' : '']">{{ item.value }}</span>
        </div>
      </div>
    </div>

    <!-- Content Section (Payload or Error) -->
    <div class="bg-base-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-3">
        <div>
          <h4 class="font-medium text-base-content">{{ contentToShow.title }}</h4>
          <p class="text-xs text-base-content/60 mt-1">{{ getContentSize(contentToShow.content) }}</p>
        </div>
        <button 
          type="button"
          @click="copyToClipboard(contentToShow.content)"
          class="btn btn-outline btn-xs"
          :class="{ 'btn-success': copySuccess }"
        >
          <svg v-if="!copySuccess" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <svg v-else class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          {{ copySuccess ? 'Copied!' : 'Copy' }}
        </button>
      </div>
      
      <div class="bg-base-100 border border-base-300 rounded-lg overflow-hidden">
        <div class="max-h-96 overflow-y-auto">
          <pre 
            class="p-4 text-sm font-mono leading-relaxed whitespace-pre-wrap break-words"
            :class="contentToShow.type === 'error' ? 'text-error' : 'text-base-content'"
          >{{ contentToShow.content }}</pre>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-2 pt-4">
      <button 
        type="button"
        @click="emit('close')"
        class="btn btn-primary"
      >
        Close
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for content */
.max-h-96::-webkit-scrollbar {
  width: 8px;
}

.max-h-96::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

.max-h-96::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.2);
  border-radius: 4px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.3);
}
</style>
